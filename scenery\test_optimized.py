#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的景点提取系统
验证是否能正确过滤餐厅、酒店等非景点内容
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入优化后的函数
try:
    from processScenery_copy_2 import preprocess_text, final_filter_non_attractions
    print("✅ 成功导入优化后的函数")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    # 尝试直接导入
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("processScenery_copy_2", "processScenery copy 2.py")
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        preprocess_text = module.preprocess_text
        final_filter_non_attractions = module.final_filter_non_attractions
        print("✅ 通过文件路径成功导入函数")
    except Exception as e2:
        print(f"❌ 文件路径导入也失败: {e2}")
        sys.exit(1)

def test_preprocess_filtering():
    """测试预处理过滤功能"""
    print("\n=== 测试预处理过滤功能 ===")
    
    # 测试用例：包含餐厅的文本
    test_cases = [
        "今天去了点都德茶餐厅吃早茶，然后参观了陈家祠。",
        "住在广州花园酒店，去了陶陶居酒家吃饭，最后逛了沙面。",
        "在深圳看了陈丹青的画展，去了深圳babycity1618艺术空间。",
        "参观了故宫博物院，在王府井大街购物，住在北京饭店。",
        "去了白云山，在山顶俯瞰广州市区，风景很美。"
    ]
    
    for i, text in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"原文: {text}")
        processed = preprocess_text(text)
        print(f"处理后: {processed}")
        print(f"是否被过滤: {'是' if not processed or len(processed) < len(text) * 0.5 else '否'}")

def test_final_filtering():
    """测试最终过滤功能"""
    print("\n=== 测试最终过滤功能 ===")
    
    # 测试用例：混合景点和非景点
    test_cases = [
        ["故宫", "点都德", "天安门", "广州花园酒店"],
        ["陈家祠", "陶陶居酒家", "沙面", "银灯食府"],
        ["白云山", "正佳广场", "越秀公园", "许留山"],
        ["陈丹青的画展", "深圳babycity1618艺术空间", "大梅沙"],
        ["长隆野生动物世界", "长隆欢乐世界", "广州塔"]
    ]
    
    for i, scenery_list in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {scenery_list}")
        filtered = final_filter_non_attractions(scenery_list)
        print(f"过滤后: {filtered}")
        removed = [item for item in scenery_list if item not in filtered]
        if removed:
            print(f"移除的非景点: {removed}")
        else:
            print("没有移除任何内容")

def main():
    """主测试函数"""
    print("🚀 开始测试优化后的景点提取系统")
    
    try:
        test_preprocess_filtering()
        test_final_filtering()
        print("\n✅ 所有测试完成！")
        print("\n📊 测试总结:")
        print("- 预处理过滤：检查是否能在文本预处理阶段过滤包含餐厅、酒店的句子")
        print("- 最终过滤：检查是否能在最后阶段移除餐厅、酒店等非景点内容")
        print("- 优化目标：消除点都德、陶陶居酒家、广州花园酒店等误识别问题")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
