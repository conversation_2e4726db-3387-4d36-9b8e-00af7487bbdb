#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试优化后的过滤功能
"""

import re

def final_filter_non_attractions(scenery_list):
    """
    最终过滤非景点内容 - 作为最后一道防线
    """
    if not scenery_list:
        return []
    
    # 严格的非景点关键词
    strict_exclude = [
        # 住宿类
        '酒店', '宾馆', '旅馆', '客栈', '民宿', '度假村', '大酒店', '国际酒店',
        # 餐饮类
        '餐厅', '餐馆', '酒家', '食府', '茶馆', '咖啡厅', '小吃店', '火锅店',
        '砂锅店', '甜品店', '奶茶店', '茶餐厅', '海鲜店', '烧腊店', '牛肉店',
        '鱼生店', '粤菜馆', '川菜馆', '湘菜馆', '西餐厅', '日料店', '韩料店',
        # 商业类
        '商场', '购物中心', '超市', '专卖店', '商店', '市场', '百货', '商城',
        # 交通类
        '机场', '火车站', '汽车站', '地铁站', '码头', '港口',
        # 展览类
        '画展', '展览', '艺术空间', '工作室', '私人收藏'
    ]
    
    # 餐厅名称模式（正则表达式）
    restaurant_patterns = [
        r'.*酒家.*', r'.*食府.*', r'.*茶馆.*', r'.*餐厅.*', r'.*砂锅店.*',
        r'.*牛肉.*店.*', r'.*甜品.*', r'.*咖啡.*', r'.*奶茶.*', r'.*小吃.*',
        r'.*酒店.*', r'.*宾馆.*', r'.*商场.*', r'.*购物.*', r'.*画展.*',
        r'.*海鲜.*店.*', r'.*烧腊.*', r'.*粤菜.*', r'.*川菜.*', r'.*湘菜.*'
    ]
    
    filtered = []
    for spot in scenery_list:
        spot = spot.strip()
        if not spot:
            continue
            
        # 检查关键词
        is_excluded = False
        for keyword in strict_exclude:
            if keyword in spot:
                is_excluded = True
                break
                
        # 检查模式
        if not is_excluded:
            for pattern in restaurant_patterns:
                if re.search(pattern, spot):
                    is_excluded = True
                    break
                    
        if not is_excluded:
            filtered.append(spot)
            
    return filtered

def test_filtering():
    """测试过滤功能"""
    print("🚀 测试优化后的景点过滤功能")
    print("=" * 50)
    
    # 从日志中发现的问题案例
    test_cases = [
        # 日志第2行的问题
        ["广州", "点都德(喜粤楼店)", "陶陶居酒家(第十甫路总店)", "广州酒家(文昌店)", "银灯食府"],
        
        # 日志第21行的问题  
        ["汕头国际大酒店", "南山湾", "乌记鲜活牛肉城(金砂东路店)"],
        
        # 其他常见问题
        ["故宫", "广州花园酒店", "天安门", "许留山"],
        ["白云山", "正佳广场", "越秀公园", "陈丹青的画展"],
        ["长隆野生动物世界", "深圳babycity1618艺术空间", "大梅沙"],
        
        # 纯景点（应该全部保留）
        ["故宫", "天安门", "长城", "颐和园"],
        
        # 纯非景点（应该全部过滤）
        ["点都德", "陶陶居酒家", "广州花园酒店", "正佳广场"]
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {case}")
        
        filtered = final_filter_non_attractions(case)
        print(f"过滤后: {filtered}")
        
        removed = [item for item in case if item not in filtered]
        if removed:
            print(f"✅ 成功移除非景点: {removed}")
        else:
            print("ℹ️  没有移除任何内容")
            
        # 分析结果
        if len(filtered) == 0 and any(keyword in '|'.join(case) for keyword in ['酒店', '餐厅', '酒家', '食府', '商场', '画展']):
            print("✅ 正确：全部为非景点，已完全过滤")
        elif len(filtered) == len(case) and not any(keyword in '|'.join(case) for keyword in ['酒店', '餐厅', '酒家', '食府', '商场', '画展']):
            print("✅ 正确：全部为景点，保持不变")
        elif len(filtered) < len(case):
            print("✅ 正确：混合内容，已过滤非景点")
        
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    print("- 目标：解决日志中发现的餐厅、酒店误识别问题")
    print("- 重点过滤：点都德、陶陶居酒家、广州花园酒店、汕头国际大酒店等")
    print("- 保留景点：故宫、天安门、白云山、长隆野生动物世界等")

if __name__ == "__main__":
    test_filtering()
